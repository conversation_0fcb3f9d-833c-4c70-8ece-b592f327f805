<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
    <div class="flex items-center justify-between mb-4">
      <div>
        <h2 class="text-lg font-medium text-gray-900 dark:text-white">在线人数统计</h2>
        <div class="flex items-center mt-1">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">
            {{ formatNumber(Math.round(currentValue)) }}
          </p>
          <span
            class="ml-2 px-2 py-1 text-sm rounded-full"
            :class="changeClass"
          >
            {{ Number(change) > 0 ? '+' : '' }}{{ Number(change).toFixed(2) }}%
          </span>
          <button
            @click="$emit('refresh')"
            class="ml-3 p-1 text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400"
            title="刷新数据"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>

      <!-- 时间范围选择 -->
      <div class="relative" ref="dropdownRef">
        <button
          @click="showPeriodDropdown = !showPeriodDropdown"
          class="px-3 py-1 text-sm rounded-md flex items-center space-x-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600"
        >
          <span>{{ getCurrentPeriodLabel() }}</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        <!-- 下拉菜单 -->
        <div
          v-if="showPeriodDropdown"
          class="absolute right-0 mt-1 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 py-1 w-48 border border-gray-200 dark:border-gray-700"
        >
          <div class="max-h-60 overflow-y-auto">
            <button
              v-for="option in periodOptions"
              :key="option.value"
              @click="changePeriod(option.value); showPeriodDropdown = false"
              class="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
              :class="selectedPeriod === option.value
                ? 'bg-primary-50 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                : 'text-gray-700 dark:text-gray-300'"
            >
              {{ option.label }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="h-64 relative">
      <!-- 加载状态 -->
      <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-white/50 dark:bg-gray-800/50 z-10">
        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"></div>
      </div>

      <!-- 图表 -->
      <canvas
        ref="chartRef"
        class="w-full h-full"
        style="position: relative; z-index: 1;"
        @mousemove="handleMouseMove"
        @click="handleClick"
      ></canvas>
    </div>

    <!-- 不再需要额外的时间轴标签，因为图表已经显示了X轴标签 -->
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
// 修改导入方式，避免 vite 构建时的解析问题
import { Chart, registerables } from 'chart.js'
import 'chartjs-adapter-date-fns' // 时间轴适配器
// 注册所有需要的组件
Chart.register(...registerables)

// 接收属性
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  period: {
    type: String,
    default: '1d'
  },
  loading: {
    type: Boolean,
    default: false
  },
  currentValue: {
    type: Number,
    default: 0
  },
  change: {
    type: Number,
    default: 0,
    validator: function(value) {
      // 如果收到字符串，尝试转换为数字
      if (typeof value === 'string') {
        console.warn('PlayersChart: change prop received string value, expected number');
        return !isNaN(Number(value));
      }
      return true;
    }
  }
})

// 事件
const emit = defineEmits(['period-change', 'refresh'])

// 状态
const chartRef = ref(null)
const chart = ref(null)
const selectedPeriod = ref(props.period)
const showPeriodDropdown = ref(false)

// 获取当前选中的时间段标签
const getCurrentPeriodLabel = () => {
  const option = periodOptions.find(opt => opt.value === selectedPeriod.value)
  return option ? option.label : '1天'
}

// 时间段选项（暂定为1天/7天）
const periodOptions = [
  { label: '1天', value: '1d' },
  { label: '7天', value: '7d' }
]

// 计算属性
const changeClass = computed(() => {
  // 确保 change 被当作数字处理
  const changeValue = Number(props.change)
  return changeValue > 0
    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
})

// 时间轴标签
const timeAxisLabels = computed(() => {
  if (!props.data || props.data.length === 0) return []

  // 根据数据点数量决定显示多少个标签
  const dataLength = props.data.length

  // 如果数据点太少，就全部显示
  if (dataLength <= 5) {
    return props.data.map(point => formatTime(point.timestamp))
  }

  // 否则选择均匀分布的几个点
  const labels = []
  const step = Math.max(1, Math.floor(dataLength / 5)) // 最多显示5个标签

  for (let i = 0; i < dataLength; i += step) {
    if (labels.length < 5) { // 限制最多5个标签
      labels.push(formatTime(props.data[i].timestamp))
    }
  }

  // 确保包含最后一个点
  const lastLabel = formatTime(props.data[dataLength - 1].timestamp)
  if (labels[labels.length - 1] !== lastLabel) {
    labels.push(lastLabel)
  }

  return labels
})

// 格式化数字
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(2) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 计算Y轴最小值
const calculateYAxisMin = (data) => {
  if (!data || data.length === 0) return 0

  try {
    // 找出数据中的最小值
    const minValue = Math.min(...data.map(point => point.value))

    // 如果最小值已经是整数，则减1；否则向下取整
    const floorValue = Math.floor(minValue)

    // 确保最小值不小于0
    return Math.max(0, floorValue - 1)
  } catch (error) {
    console.error('计算Y轴最小值出错:', error)
    return 0
  }
}

// 计算Y轴最大值
const calculateYAxisMax = (data) => {
  if (!data || data.length === 0) return 10

  try {
    // 找出数据中的最大值
    const maxValue = Math.max(...data.map(point => point.value))

    // 向上取整并加1
    const ceilValue = Math.ceil(maxValue)

    // 如果数据范围太小，确保至少有一定的范围
    const minValue = Math.min(...data.map(point => point.value))
    const range = ceilValue - Math.floor(minValue)

    // 最小范围为5
    if (range < 5) {
      return ceilValue + (5 - range)
    }

    return ceilValue + 1
  } catch (error) {
    console.error('计算Y轴最大值出错:', error)
    return 10
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp)

  // 获取格式化的小时和分钟
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')

  // 根据不同的时间范围使用不同的格式
  switch(selectedPeriod.value) {
    case '1d':
    case '24h':
      // 1天：显示小时和分钟
      return `${hours}:${minutes}`

    case '7d':
      // 7天：显示日期和小时
      return `${month}/${day} ${hours}:00`

    // 兼容旧的时间范围
    case '1h':
    case '6h':
      return `${hours}:${minutes}`

    case '3d':
      return `${month}/${day} ${hours}:${minutes}`

    case '14d':
    case '30d':
    case '90d':
      return `${month}/${day}`

    default:
      return `${hours}:${minutes}`
  }
}

// 更改时间段
const changePeriod = (period) => {
  selectedPeriod.value = period
  emit('period-change', period)
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  // 销毁旧图表
  if (chart.value) {
    chart.value.destroy()
  }

  // 处理数据，确保所有值都是整数
  const displayData = props.data.map(point => ({
    ...point,
    value: Math.round(point.value) // 确保值为整数
  }))

  console.log('Chart data for tooltip:', displayData.slice(0, 3)) // 调试信息

  // 准备图表数据 - 使用时间轴数据格式
  const chartData = {
    datasets: [{
      label: '在线人数',
      data: displayData.map(point => {
        const dataPoint = {
          x: point.timestamp, // 使用时间戳作为X轴值
          y: point.value
        }
        return dataPoint
      }),
      borderColor: '#0ea5e9',
      backgroundColor: 'rgba(14, 165, 233, 0.1)',
      borderWidth: 2,
      fill: true,
      tension: 0.4,
      pointRadius: 1, // 显示小点，体现每个数据点
      pointHoverRadius: 4,
      pointBackgroundColor: '#0ea5e9',
      pointBorderColor: '#ffffff',
      pointBorderWidth: 1
    }]
  }

  // 创建图表
  const ctx = chartRef.value.getContext('2d')

  console.log('Creating chart with data:', chartData.datasets[0].data.slice(0, 3)) // 调试信息

  chart.value = new Chart(ctx, {
    type: 'line',
    data: chartData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          enabled: true,
          mode: 'nearest',
          intersect: false,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: '#0ea5e9',
          borderWidth: 1,
          displayColors: false,
          callbacks: {
            title: function(context) {
              console.log('🎯 Tooltip title callback triggered!', context)
              if (context && context.length > 0) {
                const dataPoint = context[0]
                if (dataPoint.parsed && dataPoint.parsed.x) {
                  const timestamp = dataPoint.parsed.x
                  const date = new Date(timestamp)
                  return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                  })
                }
              }
              return '无效时间'
            },
            label: function(context) {
              console.log('🏷️ Tooltip label callback triggered!', context)
              if (context && context.parsed && context.parsed.y !== undefined) {
                const value = context.parsed.y
                return '在线人数: ' + formatNumber(Math.round(value))
              }
              return '无效数值'
            }
          }
        }
      },
      scales: {
        x: {
          type: 'time', // 使用时间轴
          display: true,
          grid: {
            display: true,
            color: 'rgba(160, 174, 192, 0.1)'
          },
          time: {
            unit: 'hour',
            stepSize: 3, // 每3小时一个标签
            displayFormats: {
              hour: 'HH:mm',
              day: 'MM/dd HH:mm' // 修复：使用小写 dd
            },
            tooltipFormat: 'yyyy-MM-dd HH:mm:ss' // 修复：使用 yyyy 而不是 YYYY
          },
          // 设置时间范围
          min: displayData.length > 0 ? displayData[0].timestamp : undefined,
          max: displayData.length > 0 ? displayData[displayData.length - 1].timestamp : undefined,
          ticks: {
            source: 'auto',
            maxRotation: 0,
            callback: function(value, index, values) {
              const date = new Date(value)
              const hours = date.getHours().toString().padStart(2, '0')
              const minutes = date.getMinutes().toString().padStart(2, '0')

              // 根据时间范围调整显示格式
              switch(selectedPeriod.value) {
                case '1d':
                case '24h':
                  return `${hours}:${minutes}`
                case '7d':
                  const month = (date.getMonth() + 1).toString().padStart(2, '0')
                  const day = date.getDate().toString().padStart(2, '0')
                  return `${month}/${day} ${hours}:00`
                default:
                  return `${hours}:${minutes}`
              }
            }
          }
        },
        y: {
          // 设置Y轴范围
          min: calculateYAxisMin(displayData),
          max: calculateYAxisMax(displayData),
          beginAtZero: false,
          grid: {
            color: 'rgba(160, 174, 192, 0.1)'
          },
          ticks: {
            // 确保只显示整数刻度
            stepSize: 1,
            precision: 0,
            callback: function(value) {
              // 只返回整数值
              if (Number.isInteger(value)) {
                return formatNumber(Math.floor(value))
              }
              return ''
            }
          }
        }
      },
      // 增强交互性
      interaction: {
        mode: 'nearest',
        intersect: false
      },
      // 鼠标悬停效果
      onHover: (event, activeElements) => {
        console.log('Chart hover event', activeElements)
        if (event.native && event.native.target) {
          event.native.target.style.cursor = activeElements.length > 0 ? 'pointer' : 'default'
        }
      }
    }
  })
}

// 事件处理函数
const handleMouseMove = (event) => {
  console.log('🐭 Canvas mouse move event', event)
}

const handleClick = (event) => {
  console.log('🐭 Canvas click event', event)
}

// 监听数据变化
watch(() => props.data, () => {
  initChart()
}, { deep: true })

// 监听 period 属性变化
watch(() => props.period, (newPeriod) => {
  selectedPeriod.value = newPeriod
}, { immediate: true })

// 点击外部关闭下拉菜单
const dropdownRef = ref(null)

const closeDropdownOnOutsideClick = (event) => {
  // 检查点击的元素是否在下拉菜单内
  if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
    showPeriodDropdown.value = false
  }
}

// 组件挂载
onMounted(() => {
  console.log('🚀 PlayersChart component mounted')
  console.log('📊 Props data length:', props.data.length)
  console.log('📊 First 3 data points:', props.data.slice(0, 3))

  // 稍微延迟初始化，确保 DOM完全渲染
  setTimeout(() => {
    console.log('📈 Initializing chart...')
    initChart()
    console.log('📈 Chart initialized')
  }, 100)

  // 添加全局点击事件监听器
  document.addEventListener('click', closeDropdownOnOutsideClick)
})

// 组件卸载
onUnmounted(() => {
  console.log('🗑️ PlayersChart component unmounting')
  if (chart.value) {
    chart.value.destroy()
    console.log('🗑️ Chart destroyed')
  }
  // 移除全局点击事件监听器
  document.removeEventListener('click', closeDropdownOnOutsideClick)
})
</script>
